# 日报页面生成系统

这是一个基于Flask的Web应用，用于创建和预览日报页面。用户可以填入各种信息，然后生成美观的日报预览页面。

## 功能特性

- **信息填写页面**：美观的表单界面，支持填入以下信息：
  - 日期
  - 头条要闻（标题和内容）
  - 数图聚焦（标题和内容）
  - 事件概览
  - 新闻列表（标题和内容）
  - 每日事件（行业和事件内容）
  - 报告图表（标题和地址）
  - 主体索引（名字、身份、ID）

- **预览页面**：仿照日报格式的预览页面，包含：
  - 专业的头部导航
  - 左右分栏布局
  - 美观的卡片式内容展示
  - 响应式设计，支持移动端
  - 打印功能

## 项目结构

```
daliy_html/
├── app.py                 # Flask主应用文件
├── requirements.txt       # Python依赖包
├── README.md             # 项目说明文档
├── templates/            # HTML模板文件夹
│   ├── form.html         # 信息填写表单页面
│   └── preview.html      # 日报预览页面
└── static/               # 静态资源文件夹
    └── css/
        ├── style.css     # 表单页面样式
        └── preview.css   # 预览页面样式
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
python app.py
```

应用将在 `http://127.0.0.1:5000` 启动。

### 3. 使用说明

1. 打开浏览器访问 `http://127.0.0.1:5000`
2. 在表单页面填入所需信息
3. 点击"生成预览"按钮
4. 查看生成的日报预览页面
5. 可以点击"打印报告"进行打印，或"返回编辑"继续修改

## 部署说明

### 本地部署
直接运行 `python app.py` 即可在本地启动服务。

### 生产环境部署
建议使用 Gunicorn 等WSGI服务器：

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Docker部署
可以创建Dockerfile进行容器化部署：

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

## 技术栈

- **后端**: Flask (Python)
- **前端**: HTML5, CSS3, JavaScript
- **样式**: 响应式设计，支持移动端
- **部署**: 支持多种部署方式

## 特色功能

1. **动态表单**: 支持动态添加和删除表单项
2. **美观界面**: 现代化的UI设计，渐变色彩搭配
3. **响应式布局**: 自适应不同屏幕尺寸
4. **打印支持**: 专门优化的打印样式
5. **易于部署**: 最小化依赖，方便部署

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

MIT License
