<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股权投资市场每日资讯 - {{ data.date or '未知日期' }}</title>
</head>
<body style="font-family: 'Microsoft YaHei', 'SimSun', Arial, sans-serif; background: #1e3a5f; color: #333; line-height: 1.4; margin: 0; padding: 0;">
    <div class="report-container" style="max-width: 800px; margin: 0 auto; background: white; min-height: 100vh;">
        <!-- 头部 -->
        <div class="header" style="color: #333; padding: 0; border-bottom: 1px solid #dee2e6;">
            <div class="header-top" style="background: white; padding: clamp(12px, 4vw, 20px) clamp(15px, 5vw, 30px); border-bottom: 1px solid #e9ecef;">
                <div class="logo-section" style="display: flex; align-items: center;">
                    <div class="logo-container" style="width: clamp(45px, 8vw, 60px); height: clamp(45px, 8vw, 60px); background: #4a90e2; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: clamp(12px, 2vw, 15px); box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);">
                        <img src="{{ logo_base64 }}" alt="嘉川数据" class="logo-image" style="width: clamp(28px, 6vw, 40px); height: clamp(28px, 6vw, 40px); object-fit: contain; filter: brightness(0) invert(1);">
                    </div>
                    <div class="company-info" style="display: flex; flex-direction: column;">
                        <div class="company-name-cn" style="font-size: clamp(1.1em, 3vw, 1.4em); font-weight: 600; color: #333; margin-bottom: 2px;">嘉川数据</div>
                        <div class="company-name-en" style="font-size: clamp(0.8em, 2vw, 0.9em); color: #666; font-weight: 400; letter-spacing: 1px;">JIA CHUAN</div>
                    </div>
                </div>
            </div>
            <div class="header-bottom" style="padding: clamp(15px, 4vw, 25px) clamp(15px, 5vw, 30px); display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
                <div class="report-title" style="font-size: clamp(1.2em, 4vw, 1.8em); font-weight: 600; color: #2c3e50; letter-spacing: 1px; flex: 1; min-width: 200px;">股权投资市场每日资讯</div>
                <div class="report-date" style="font-size: clamp(1em, 2.5vw, 1.1em); color: #6c757d; font-weight: 500; white-space: nowrap;">{{ data.date }}</div>
            </div>
        </div>
        <!-- 今日导读 -->
        <div class="today-guide" style="background: white; border-radius: 0; padding: clamp(15px, 4vw, 25px) clamp(15px, 5vw, 30px); margin-bottom: 0;">
            <div class="guide-header">
                <h2 class="guide-title" style="color: #2c3e50; font-size: clamp(1.3em, 3.5vw, 1.6em); font-weight: 600; margin-bottom: clamp(15px, 4vw, 25px); padding-bottom: 0; border-bottom: none;">今日导读</h2>
            </div>
            <div class="guide-content">
                <div class="guide-item" style="display: flex; align-items: flex-start; margin-bottom: clamp(15px, 3vw, 20px); font-size: clamp(0.9em, 2.5vw, 1em); line-height: 1.6; flex-wrap: wrap;">
                    <div class="guide-icon" style="width: clamp(18px, 4vw, 20px); height: clamp(18px, 4vw, 20px); margin-right: clamp(8px, 2vw, 12px); margin-top: 2px; flex-shrink: 0;">
                        <img src="{{ icons['u27.png'] }}" alt="头条要闻" style="width: 100%; height: 100%;">
                    </div>
                    <div class="guide-text" style="color: #2c3e50; flex: 1; font-weight: 400; min-width: 0;">
                        <span class="guide-label" style="color: #2c3e50; font-weight: 600; margin-right: 8px; flex-shrink: 0; font-size: 1em;">头条要闻：</span>
                        <span class="guide-value" style="word-break: break-word;">{{ data.headline_news }}</span>
                    </div>
                </div>
                <div class="guide-item" style="display: flex; align-items: flex-start; margin-bottom: clamp(15px, 3vw, 20px); font-size: clamp(0.9em, 2.5vw, 1em); line-height: 1.6; flex-wrap: wrap;">
                    <div class="guide-icon" style="width: clamp(18px, 4vw, 20px); height: clamp(18px, 4vw, 20px); margin-right: clamp(8px, 2vw, 12px); margin-top: 2px; flex-shrink: 0;">
                        <img src="{{ icons['u28.png'] }}" alt="数图聚焦" style="width: 100%; height: 100%;">
                    </div>
                    <div class="guide-text" style="color: #2c3e50; flex: 1; font-weight: 400; min-width: 0;">
                        <span class="guide-label" style="color: #2c3e50; font-weight: 600; margin-right: 8px; flex-shrink: 0; font-size: 1em;">数图聚焦：</span>
                        <span class="guide-value" style="word-break: break-word;">{{ data.data_focus }}</span>
                    </div>
                </div>
                <div class="guide-item" style="display: flex; align-items: flex-start; margin-bottom: 0; font-size: clamp(0.9em, 2.5vw, 1em); line-height: 1.6; flex-wrap: wrap;">
                    <div class="guide-icon" style="width: clamp(18px, 4vw, 20px); height: clamp(18px, 4vw, 20px); margin-right: clamp(8px, 2vw, 12px); margin-top: 2px; flex-shrink: 0;">
                        <img src="{{ icons['u29.png'] }}" alt="事件概览" style="width: 100%; height: 100%;">
                    </div>
                    <div class="guide-text" style="color: #2c3e50; flex: 1; font-weight: 400; min-width: 0;">
                        <span class="guide-label" style="color: #2c3e50; font-weight: 600; margin-right: 8px; flex-shrink: 0; font-size: 1em;">事件概览：</span>
                        <span class="guide-value" style="word-break: break-word;">{{ data.event_overview }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content" style="display: flex; gap: clamp(0px, 3vw, 20px); padding: 0 clamp(15px, 3vw, 0px); background: white; flex-direction: row; flex-wrap: wrap;">
            <!-- 左侧内容 -->
            <div class="left-column" style="flex: 2; padding: 0; min-width: 300px; width: 100%;">
                <!-- 新闻内容 -->
                {% if data.news_list %}
                <div class="content-section" style="background: white; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: clamp(15px, 3vw, 20px); overflow: hidden;">
                    <div class="section-header" style="background: #f8f9fa; padding: clamp(10px, 2vw, 12px) clamp(12px, 3vw, 15px); border-bottom: 1px solid #e9ecef;">
                        <h3 class="section-title" style="color: #333; font-size: clamp(1.1em, 3vw, 1.3em); font-weight: 600; margin: 0;">新闻内容</h3>
                    </div>
                    <div class="section-content" style="padding: clamp(12px, 3vw, 15px);">
                        {% for news in data.news_list %}
                        <div class="news-item" style="margin-bottom: clamp(20px, 4vw, 30px); padding-bottom: clamp(15px, 3vw, 20px); border-bottom: 1px solid #f0f0f0;">
                            <!-- 第一部分：标题 -->
                            <h4 class="news-title" style="color: #333; font-size: clamp(1em, 2.5vw, 1.1em); font-weight: 600; line-height: 1.4; margin: 0 0 clamp(12px, 3vw, 15px) 0; word-break: break-word;">{{ news.title }}</h4>
                            <!-- 第二部分：图片 -->
                            <div class="news-image-section" style="margin-bottom: clamp(12px, 3vw, 15px);">
                                {% if news.image %}
                                <div class="news-image" style="width: 100%; margin-bottom: clamp(12px, 3vw, 15px);">
                                    <img src="{{ news.image|to_base64 }}" alt="{{ news.title }}" class="news-img" style="width: 100%; height: auto; max-height: clamp(200px, 40vw, 300px); object-fit: cover; border-radius: 4px; border: 1px solid #e9ecef;">
                                </div>
                                {% else %}
                                <div class="news-image-placeholder" style="width: 100%; height: clamp(150px, 30vw, 200px); background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: clamp(0.8em, 2vw, 0.9em);">
                                    图片占位区域
                                </div>
                                {% endif %}
                            </div>
                            <!-- 第三部分：内容 -->
                            <div class="news-content-section" style="margin-top: 0;">
                                <p class="news-text" style="color: #666; font-size: clamp(0.85em, 2.2vw, 0.9em); line-height: 1.6; margin-bottom: clamp(15px, 3vw, 20px); text-align: justify; white-space: normal; word-break: break-word;">{{ news.content|nl2br }}</p>
                                <!-- 底部按钮和分享 -->
                                <div class="news-footer" style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: clamp(8px, 2vw, 10px);">
                                    {% if news.url %}
                                    <a href="{{ news.url }}" target="_blank" class="read-full-btn" style="background: #007bff; color: white; border: none; padding: clamp(6px, 1.5vw, 8px) clamp(12px, 3vw, 16px); border-radius: 20px; font-size: clamp(0.8em, 2vw, 0.85em); font-weight: 500; cursor: pointer; text-decoration: none; display: inline-block; white-space: nowrap;">阅读全文</a>
                                    {% else %}
                                    <button class="read-full-btn" style="background: #ccc; color: white; border: none; padding: clamp(6px, 1.5vw, 8px) clamp(12px, 3vw, 16px); border-radius: 20px; font-size: clamp(0.8em, 2vw, 0.85em); font-weight: 500; cursor: not-allowed; white-space: nowrap;" disabled>阅读全文</button>
                                    {% endif %}
                                    <div class="share-wechat" style="display: flex; align-items: center; gap: clamp(6px, 1.5vw, 8px);">
                                        <span class="share-label" style="color: #666; font-size: clamp(0.8em, 2vw, 0.85em); white-space: nowrap;">分享：</span>
                                        <img src="{{ icons['u14.png'] }}" alt="分享到微信" class="wechat-icon" style="width: clamp(20px, 4vw, 24px); height: clamp(20px, 4vw, 24px); cursor: pointer;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- 每日事件 -->
                {% if data.daily_events %}
                <div class="content-section daily-events-section" style="background: white; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 0; overflow: hidden;">
                    <div class="section-header" style="background: #f8f9fa; padding: clamp(10px, 2vw, 12px) clamp(12px, 3vw, 15px); border-bottom: 1px solid #e9ecef;">
                        <h3 class="section-title" style="color: #333; font-size: clamp(1.1em, 3vw, 1.3em); font-weight: 600; margin: 0;">每日事件</h3>
                    </div>
                    <div class="section-content" style="padding: clamp(12px, 3vw, 15px);">
                        {% for event in data.daily_events %}
                        <div class="event-item" style="margin-bottom: clamp(15px, 3vw, 20px);">
                            <div class="event-industry" style="font-weight: 600; font-size: clamp(1em, 2.8vw, 1.2em); margin-bottom: clamp(10px, 2vw, 12px); line-height: 1.4; word-break: break-word;">{{ event.industry }}</div>
                            <div class="event-content" style="color: #666; font-size: clamp(0.85em, 2.2vw, 0.9em); line-height: 1.6; text-align: justify; margin-bottom: 0; word-break: break-word;">{{ event.content|nl2br }}</div>
                            <div class="event-details-link" style="padding-top: clamp(4px, 1vw, 5px); padding-bottom: clamp(4px, 1vw, 5px);">
                                <a href="#" class="view-details" style="color: #4a90e2; font-size: clamp(0.85em, 2.2vw, 0.9em); text-decoration: none; font-weight: 500;">查看交易详情&gt;</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- 数据图表 -->
                {% if data.chart_title or data.chart_image %}
                <div class="content-section" style="background: white; border: 1px solid #e9ecef; border-radius: 5px; margin-bottom: 0; overflow: hidden;">
                    <div class="section-header" style="background: #f8f9fa; padding: clamp(10px, 2vw, 12px) clamp(12px, 3vw, 15px); border-bottom: 1px solid #e9ecef;">
                        <h3 class="section-title" style="color: #333; font-size: clamp(1.1em, 3vw, 1.3em); font-weight: 600; margin: 0;">数据图表</h3>
                    </div>
                    <div class="section-content" style="padding: clamp(12px, 3vw, 15px);">
                        <div class="chart-item">
                            <div class="chart-image-container" style="margin-bottom: clamp(12px, 3vw, 15px);">
                                {% if data.chart_image %}
                                <div class="chart-image" style="width: 100%; text-align: center;">
                                    <img src="{{ data.chart_image|to_base64 }}" alt="{{ data.chart_title or '数据图表' }}" class="chart-img" style="max-width: 100%; height: auto; border-radius: 4px; border: 1px solid #e9ecef;">
                                </div>
                                {% else %}
                                <div class="chart-image-placeholder" style="width: 100%; height: clamp(200px, 40vw, 300px); background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: clamp(0.8em, 2vw, 0.9em);">
                                    图表图片占位区域
                                </div>
                                {% endif %}
                            </div>
                            {% if data.chart_title and data.chart_url %}
                            <div class="chart-source" style="color: #666; font-size: clamp(0.8em, 2vw, 0.85em); text-align: center;">
                               源自： <a href="{{ data.chart_url }}" target="_blank" class="chart-source-link" style="color: #4a90e2; text-decoration: none; font-weight: 500; word-break: break-word;">{{ data.chart_title }}</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- 右侧边栏 -->
            <div class="right-sidebar" style="flex: 1; padding: 0; margin-top: clamp(0px, 3vw, 0px); min-width: 250px; width: 100%;">
                <!-- 主体索引 -->
                {% if data.main_index %}
                <div class="sidebar-section" style="background: white; border: 1px solid #e9ecef; border-radius: 5px; overflow: hidden;">
                    <h3 class="sidebar-title" style="background: #f8f9fa; color: #333; font-size: clamp(1.1em, 3vw, 1.3em); font-weight: 600; margin: 0; padding: clamp(10px, 2vw, 12px) clamp(12px, 3vw, 15px); border-bottom: 1px solid #e9ecef;">主体索引</h3>
                    <div class="index-list" style="padding: clamp(12px, 3vw, 15px);">
                        {% for index in data.main_index %}
                        <div class="index-item" style="margin-bottom: clamp(10px, 2vw, 12px); padding: clamp(8px, 2vw, 10px); background: #f8f9fa; border-radius: 4px; border-left: 3px solid #6f42c1;">
                            <div class="index-name" style="color: #333; font-weight: 600; font-size: clamp(0.8em, 2vw, 0.85em); margin-bottom: 3px;">
                                <a href="{{ index|generate_entity_url }}" target="_blank" class="entity-link" style="color: #4a90e2; text-decoration: none; font-weight: 600; transition: all 0.3s ease; word-break: break-word;">{{ index.name }}</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
