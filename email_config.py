# 邮件配置文件
# 请根据您的邮箱设置修改以下配置

EMAIL_CONFIG = {
    # SMTP服务器配置
    'smtp_server': 'smtp.qq.com',  # QQ邮箱SMTP服务器
    'smtp_port': 587,              # SMTP端口
    
    # 发送方邮箱配置
    'sender_email': '<EMAIL>',        # 发送方邮箱地址
    'sender_password': 'jmsveusegffgbcif',     # 邮箱授权码（不是登录密码）
    
    # 收件人邮箱
    'recipient_email': '<EMAIL>'
}

# 常用邮箱SMTP配置参考：
# QQ邮箱：smtp.qq.com, 端口587
# 163邮箱：smtp.163.com, 端口25
# Gmail：smtp.gmail.com, 端口587
# Outlook：smtp-mail.outlook.com, 端口587

# 注意事项：
# 1. sender_password 是邮箱的授权码，不是登录密码
# 2. 需要在邮箱设置中开启SMTP服务并获取授权码
# 3. QQ邮箱授权码获取方法：
#    - 登录QQ邮箱 -> 设置 -> 账户 -> POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务
#    - 开启"POP3/SMTP服务"，按提示获取授权码
